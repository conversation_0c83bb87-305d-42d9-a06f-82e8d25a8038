using LibraryAPI.AppDataContext;
using LibraryAPI.Models;
using LibraryAPI.Models.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace LibraryAPI.Services
{
    public interface IDataSeeder
    {
        Task SeedAsync();
    }

    public class DataSeeder : IDataSeeder
    {
        private readonly LibraryDbContext _dbContext;
        private readonly ILogger<DataSeeder> _logger;
        private readonly SeedingOptions _seedingOptions;

        public DataSeeder(LibraryDbContext dbContext, ILogger<DataSeeder> logger, IOptions<SeedingOptions> seedingOptions)
        {
            _dbContext = dbContext;
            _logger = logger;
            _seedingOptions = seedingOptions.Value;
        }

        public async Task SeedAsync()
        {
            if (!_seedingOptions.EnableSeeding)
            {
                _logger.LogInformation("Data seeding is disabled.");
                return;
            }

            try
            {
                // Ensure database is created
                await _dbContext.Database.EnsureCreatedAsync();

                // Seed users
                if (_seedingOptions.SeedUsers)
                {
                    await SeedUsersAsync();
                }

                // You can add more seeding methods here for other entities
                if (_seedingOptions.SeedAuthors)
                {
                    await SeedAuthorsAsync();
                }

                if (_seedingOptions.SeedBooks)
                {
                    await SeedBooksAsync();
                }

                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("Data seeding completed successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while seeding the database.");
                throw;
            }
        }

        private async Task SeedUsersAsync()
        {
            var defaultUsers = GetDefaultUsers();

            foreach (var user in defaultUsers)
            {
                if (!await _dbContext.Users.AnyAsync(u => u.Email == user.Email))
                {
                    _dbContext.Users.Add(user);
                    _logger.LogInformation("Created user: {Email} with role: {Role}", user.Email, user.Role);
                }
                else
                {
                    _logger.LogInformation("User {Email} already exists, skipping creation.", user.Email);
                }
            }
        }

        private List<User> GetDefaultUsers()
        {
            var userOptions = _seedingOptions.Users;

            return new List<User>
            {
                new User
                {
                    Name = userOptions.AdminName,
                    Email = userOptions.AdminEmail,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword(userOptions.AdminPassword),
                    Role = "Admin"
                },
                new User
                {
                    Name = userOptions.LibrarianName,
                    Email = userOptions.LibrarianEmail,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword(userOptions.LibrarianPassword),
                    Role = "Librarian"
                },
                new User
                {
                    Name = userOptions.MemberName,
                    Email = userOptions.MemberEmail,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword(userOptions.MemberPassword),
                    Role = "Member"
                }
            };
        }

        // Example of additional seeding methods you can add
        private async Task SeedAuthorsAsync()
        {
            if (!await _dbContext.Authors.AnyAsync())
            {
                var defaultAuthors = new List<Author>
                {
                    new Author { Name = "J.K. Rowling" },
                    new Author { Name = "George Orwell" },
                    new Author { Name = "Jane Austen" }
                };

                _dbContext.Authors.AddRange(defaultAuthors);
                _logger.LogInformation("Seeded {Count} default authors.", defaultAuthors.Count);
            }
        }

        private async Task SeedBooksAsync()
        {
            if (!await _dbContext.Books.AnyAsync())
            {
                // Get authors first
                var authors = await _dbContext.Authors.ToListAsync();
                if (authors.Any())
                {
                    var defaultBooks = new List<Book>
                    {
                        new Book
                        {
                            Title = "Harry Potter and the Philosopher's Stone",
                            AuthorId = authors.First(a => a.Name == "J.K. Rowling").Id
                        },
                        new Book
                        {
                            Title = "1984",
                            AuthorId = authors.First(a => a.Name == "George Orwell").Id
                        }
                    };

                    _dbContext.Books.AddRange(defaultBooks);
                    _logger.LogInformation("Seeded {Count} default books.", defaultBooks.Count);
                }
            }
        }
    }
}
