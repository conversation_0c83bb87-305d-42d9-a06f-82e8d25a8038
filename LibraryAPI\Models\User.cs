using System;
using System.Collections.Generic;
using LibraryAPI.Models.Common;

namespace LibraryAPI.Models
{
    public class User : Entity
    {
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string? PasswordHash { get; set; }
        public string Role { get; set; } = "Member";
        
        // Initialize the collection to avoid null reference exceptions
        public List<RefreshToken> RefreshTokens { get; set; } = new List<RefreshToken>();
    }
}
