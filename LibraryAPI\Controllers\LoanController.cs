using LibraryAPI.Contracts.Request;
using LibraryAPI.Contracts.Response;
using LibraryAPI.Models;
using LibraryAPI.Services.Interface;
using Mapster;
using MapsterMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace LibraryAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize] // Require authentication for all actions
public class LoanController : ControllerBase
{
    private readonly ILoanRepository loanRepository;
    private readonly IMemberRepository memberRepository;
    private readonly IBookRepository bookRepository;
    private readonly IMapper mapper;

    public LoanController(
        ILoanRepository loanRepository, 
        IMemberRepository memberRepository,
        IBookRepository bookRepository,
        IMapper mapper)
    {
        this.loanRepository = loanRepository;
        this.memberRepository = memberRepository;
        this.bookRepository = bookRepository;
        this.mapper = mapper;
    }

    [HttpPost]
    [Authorize(Roles = "Admin,Librarian")] // Only admins and librarians can create loans
    public async Task<IActionResult> CreateLoan([FromBody] CreateLoanRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Check if member exists
            var member = await memberRepository.GetMemberByIdAsync(request.MemberId);
            if (member == null)
            {
                return NotFound($"Member with ID {request.MemberId} not found");
            }

            // Check if book exists
            var book = await bookRepository.GetBookByIdAsync(request.BookId);
            if (book == null)
            {
                return NotFound($"Book with ID {request.BookId} not found");
            }

            // Check if member already has a loan
            var existingLoan = await loanRepository.GetLoanByMemberId(request.MemberId);
            if (existingLoan != null)
            {
                return BadRequest($"Member already has an active loan");
            }

            var loan = new Loan
            {
                MemberId = request.MemberId,
                BookId = request.BookId,
                CreatedAt = DateTime.UtcNow.ToString()
            };

            var createdLoan = await loanRepository.CreateLoan(loan);
            var loanDto = mapper.Map<LoanDto>(createdLoan);
            return Ok(loanDto);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetAllLoans()
    {
        try
        {
            var loans = await loanRepository.GetAllLoan();
            var loanDtos = mapper.Map<List<LoanDto>>(loans);
            return Ok(loanDtos);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetLoanById(Guid id)
    {
        try
        {
            var loan = await loanRepository.GetLoanById(id);
            if (loan == null)
            {
                return NotFound();
            }
            var loanDto = mapper.Map<LoanDto>(loan);
            return Ok(loanDto);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpGet("member/{memberId}")]
    public async Task<IActionResult> GetLoanByMemberId(Guid memberId)
    {
        try
        {
            var loan = await loanRepository.GetLoanByMemberId(memberId);
            if (loan == null)
            {
                return NotFound($"No active loan found for member with ID {memberId}");
            }
            var loanDto = mapper.Map<LoanDto>(loan);
            return Ok(loanDto);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,Librarian")] // Only admins and librarians can update loans
    public async Task<IActionResult> UpdateLoan(Guid id, [FromBody] CreateLoanRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Check if member exists
            var member = await memberRepository.GetMemberByIdAsync(request.MemberId);
            if (member == null)
            {
                return NotFound($"Member with ID {request.MemberId} not found");
            }

            // Check if book exists
            var book = await bookRepository.GetBookByIdAsync(request.BookId);
            if (book == null)
            {
                return NotFound($"Book with ID {request.BookId} not found");
            }

            var loan = new Loan
            {
                MemberId = request.MemberId,
                BookId = request.BookId,
                UpdatedAt = DateTime.UtcNow.ToString()
            };

            var updatedLoan = await loanRepository.UpdateLoanById(id, loan);
            if (updatedLoan == null)
            {
                return NotFound($"Loan with ID {id} not found");
            }

            var loanDto = mapper.Map<LoanDto>(updatedLoan);
            return Ok(loanDto);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")] // Only admins can delete loans
    public async Task<IActionResult> DeleteLoan(Guid id)
    {
        try
        {
            var isDeleted = await loanRepository.DeleteLoanById(id);
            if (!isDeleted)
            {
                return NotFound();
            }
            return NoContent();
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }
}


