using LibraryAPI.AppDataContext;
using LibraryAPI.Models;
using LibraryAPI.Services.Interface;
using Microsoft.EntityFrameworkCore;

namespace LibraryAPI.Services.Repository;

public class BookRepository : IBookRepository
{
    private readonly LibraryDbContext libraryDbContext;

    public BookRepository(LibraryDbContext libraryDbContext)
    {
        this.libraryDbContext = libraryDbContext;
    }

    public async Task<Book> CreateBookAsync(Book book)
    {
        var newBook = new Book
        {
            Title = book.Title,
            AuthorId = book.AuthorId,
            CreatedAt = DateTime.UtcNow.ToString()
        };
        libraryDbContext.Add(newBook);
        await libraryDbContext.SaveChangesAsync();
        return newBook;
    }

    public async Task<bool> DeleteBookAsync(Guid id)
    {
        var targetbook = await libraryDbContext.Books.FindAsync(id);
        if (targetbook == null)
        {
            return false;
        }
        targetbook.DeletedAt = DateTime.UtcNow.ToString();
        libraryDbContext.Books.Update(targetbook);
        await libraryDbContext.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<Book>> GetAllBooksAsync()
    {
        var allBooks = await libraryDbContext.Books
        .Where(b => b.DeletedAt == null)
        .ToListAsync();
        return allBooks;
    }

    public async Task<Book?> GetBookByIdAsync(Guid id)
    {
        var targetbook = await libraryDbContext.Books
        .Where(b => b.DeletedAt == null)
        .FirstOrDefaultAsync(b => b.Id == id);
        if (targetbook == null)
        {
            return null;
        }
        return targetbook;
    }

    public async Task<IEnumerable<Book>> GetBooksByAuthorIdAsync(Guid authorId)
    {
        return await libraryDbContext.Books
        .Where(b => b.DeletedAt == null && b.AuthorId == authorId)
        .ToListAsync();
    }

    public async Task<Book?> UpdateBookAsync(Guid id, Book book)
    {
        var targetbook = await libraryDbContext.Books
        .Where(b => b.DeletedAt == null)
        .FirstOrDefaultAsync(b => b.Id == id);
        if (targetbook == null)
        {
            return null;
        }
        targetbook.Title = book.Title;
        targetbook.UpdatedAt = DateTime.UtcNow.ToString();
        await libraryDbContext.SaveChangesAsync();
        return targetbook;
    }

    
}