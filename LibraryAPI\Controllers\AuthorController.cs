using LibraryAPI.Contracts.Request;
using LibraryAPI.Contracts.Response;
using LibraryAPI.Models;
using LibraryAPI.Services.Interface;
using MapsterMapper;
using Microsoft.AspNetCore.Mvc;

namespace LibraryAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthorController : ControllerBase
{
    private readonly IAuthorRepository authorRepository;
    private readonly IMapper mapper;

    public AuthorController(IAuthorRepository authorRepository, IMapper mapper)
    {
        this.authorRepository = authorRepository;
        this.mapper = mapper;
    }

    [HttpPost]
    public async Task<IActionResult> CreateAuthor([FromBody] CreateAthorRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var author = new Author
            {
                Name = request.Name
            };
            var createdAuthor = await authorRepository.Create(author);
            var authorDto = mapper.Map<AuthorDto>(createdAuthor);
            return Ok(authorDto);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetAllAuthors()
    {
        try
        {
            var authors = await authorRepository.GetAll();
            var authorDtos = mapper.Map<List<AuthorDto>>(authors);
            return Ok(authorDtos);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpGet("{Id}")]
    public async Task<IActionResult> GetAuthorById(Guid Id)
    {
        try
        {
            var author = await authorRepository.GetById(Id);
            if (author == null)
            {
                return NotFound();
            }
            var authorDto = mapper.Map<AuthorDto>(author);
            return Ok(authorDto);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpPut("{Id}")]
    public async Task<IActionResult> UpdateAuthor(Guid Id, [FromBody] UpdateAuthorRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var author = new Author
            {
                Name = request.Name
            };
            var targetAuthor = await authorRepository.UpdateById(Id, author);
            if (targetAuthor == null)
            {
                return NotFound();
            }
            var authorDto = mapper.Map<AuthorDto>(targetAuthor);
            return Ok(authorDto);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpDelete("{Id}")]
    public async Task<IActionResult> DeleteAuthor(Guid Id)
    {
        try
        {
            var isDeleted = await authorRepository.DeleteById(Id);
            if (!isDeleted)
            {
                return NoContent();
            }
            return Ok();
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }
    }
