
using Azure.Core.Serialization;
using LibraryAPI.Contracts.Request;
using LibraryAPI.Contracts.Response;
using LibraryAPI.Models;
using LibraryAPI.Services.Interface;
using MapsterMapper;
using Microsoft.AspNetCore.Mvc;

namespace LibraryAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MemberController : ControllerBase
{

    private readonly IMemberRepository memberRepository;
    private readonly IMapper mapper;

    public MemberController(IMemberRepository memberRepository, IMapper mapper)
    {
        this.memberRepository = memberRepository;
        this.mapper = mapper;
    }

    [HttpPost]
    public async Task<IActionResult> CreateMember([FromBody] CreateMemberRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var newMember = new Member
            {
                Name = request.Name
            };
            var createdMember = await memberRepository.CreateMemberAsync(newMember);
            var memberDto = mapper.Map<MemberDto>(createdMember);
            return Ok(memberDto);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetAllMembers()
    {
        try
        {
            var allmembers = await memberRepository.GetAllMembersAsync();
            var memberDtos = mapper.Map<List<MemberDto>>(allmembers);
            return Ok(memberDtos);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }


    [HttpGet("{id}")]
    public async Task<IActionResult> GetMemberById(Guid id)
    {
        try
        {
            var targetmember = await memberRepository.GetMemberByIdAsync(id);
            if(targetmember == null){
                return NotFound();
            }
            var memberDto = mapper.Map<MemberDto>(targetmember);
            return Ok(memberDto);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateMemberById(Guid id, [FromBody] UpdateMemberRequest request)
    {
        try
        {
            if(!ModelState.IsValid){
                return BadRequest(ModelState);
            }
            var member = new Member {
                Name = request.Name
            };
            var targetmember = await memberRepository.UpdateMemberByIdAsync(id, member);
            if (targetmember == null) {
                return NotFound();
            }
            var memberDto = mapper.Map<MemberDto>(targetmember);
            return Ok(memberDto);
        }
        catch(Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteMemberById(Guid id)
    {
        try
        {
            var isDeleted = await memberRepository.DeleteMemberByIdAsync(id);
            if(!isDeleted)
            {
                return NotFound();
            }
            return NoContent();
        }
        catch(Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

















    
}
