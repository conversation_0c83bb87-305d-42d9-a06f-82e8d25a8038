using LibraryAPI.AppDataContext;
using LibraryAPI.Models;
using LibraryAPI.Services.Interface;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.EntityFrameworkCore;

namespace LibraryAPI.Services.Repository;

public class AuthorRepository : IAuthorRepository
{
    private readonly LibraryDbContext libraryDbContext;

    public AuthorRepository(LibraryDbContext libraryDbContext)
    {
        this.libraryDbContext = libraryDbContext;
    }

    public async Task<Author> Create(Author author)
    {
        var newAuthor = new Author
        {
            Name = author.Name,
            CreatedAt = DateTime.UtcNow.ToString(),

        };
        libraryDbContext.Add(newAuthor);
        await libraryDbContext.SaveChangesAsync();
        return newAuthor;
    }

  
    
   public async Task<bool> DeleteById(Guid id)
{
    var targetauthor = await libraryDbContext.Authors.FindAsync(id);
    if (targetauthor == null)
    {
        return false;
    }
    targetauthor.DeletedAt = DateTime.UtcNow.ToString();
    libraryDbContext.Authors.Update(targetauthor);
    await libraryDbContext.SaveChangesAsync();
    return true;
}

    public async Task<Author?> GetById(Guid id)
    {
        var targetauthor = await libraryDbContext.Authors
            .Where(a => a.DeletedAt == null)
            .Include(a => a.Books.Where(b => b.DeletedAt == null))
            .FirstOrDefaultAsync(a => a.Id == id);
        
        if (targetauthor == null)
        {
            return null;
        }
        return targetauthor;
    }

    public async Task<IEnumerable<Author>> GetAll()
    {
        return await libraryDbContext.Authors
            .Where(a => a.DeletedAt == null)
            .Include(a => a.Books.Where(b => b.DeletedAt == null))
            .ToListAsync();
    }



    public async Task<Author?> UpdateById(Guid id, Author author)
    {
        var targetauthor = await libraryDbContext.Authors
        .Where(a => a.DeletedAt == null)
        .FirstOrDefaultAsync(a => a.Id == id);
        if (targetauthor == null)
        {
            return null;
        }
        targetauthor.Name = author.Name;
        targetauthor.UpdatedAt = DateTime.UtcNow.ToString();
        await libraryDbContext.SaveChangesAsync();
        return targetauthor;
    }

    
}
