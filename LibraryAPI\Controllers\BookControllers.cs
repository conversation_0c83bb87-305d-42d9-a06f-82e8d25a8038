using LibraryAPI.Contracts.Request;
using LibraryAPI.Models;
using LibraryAPI.Services.Interface;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace LibraryAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize] // Require authentication for all actions in this controller
public class BookController : ControllerBase
{
    private readonly IBookRepository bookRepository;
    private readonly IAuthorRepository authorRepository;

    public BookController(IBookRepository bookRepository, IAuthorRepository authorRepository)
    {
        this.bookRepository = bookRepository;
        this.authorRepository = authorRepository;
    }


    [HttpPost]
    [Authorize(Roles = "Admin,Librarian")] // Only admins and librarians can create books
    public async Task<IActionResult> CreateBook([FromBody] CreateBookRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Check if AuthorId is provided
            if (request.AuthorId == Guid.Empty)
            {
                return BadRequest("AuthorId is required");
            }

            // Check if Author exists
            var author = await authorRepository.GetById(request.AuthorId);
            if (author == null)
            {
                return NotFound($"Author with ID {request.AuthorId} not found");
            }

            var book = new Book
            {
                Title = request.Title,
                AuthorId = request.AuthorId,
                CreatedAt = DateTime.UtcNow.ToString()
            };

            var newBook = await bookRepository.CreateBookAsync(book);
            return Ok(newBook);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }


    [HttpGet]
    public async Task<IActionResult> GetAllBooks()
    {
        try
        {
            var allBooks = await bookRepository.GetAllBooksAsync();
            return Ok(allBooks);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetBookById(Guid id)
    {
        try
        {
            var targetbook = await bookRepository.GetBookByIdAsync(id);
            if (targetbook == null)
            {
                return NotFound();
            }
            return Ok(targetbook);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }

    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,Librarian")] // Admins and librarians can update books
    public async Task<IActionResult> UpdateBookById(Guid Id, [FromBody] UpdateBookRequest request)
    {

        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var book = new Book
            {
                Title = request.Title
            };
            var targetbook = await bookRepository.UpdateBookAsync(Id, book);
            if (targetbook == null)
            {
                return NotFound();
            }
            return Ok(targetbook);

        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }




    [HttpGet("byauthor/{authorId}")]
    public async Task<IActionResult> GetBooksByAuthorId(Guid authorId)
    {
        try
        {
            var books = await bookRepository.GetBooksByAuthorIdAsync(authorId);
            if (books == null || !books.Any())
            {
                return NotFound($"No books found for author with ID: {authorId}");

            }
            return Ok(books);


        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }


    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")] // Only admins can delete books
    public async Task<IActionResult> DeleteBookById(Guid Id) {
        try
        {
            var isDeleted = await bookRepository.DeleteBookAsync(Id);
            if (!isDeleted)
            {
                return NoContent();
            }
            return Ok();
        }
        catch(Exception ex)
        {
            return StatusCode(500, $"Internal Server Error: {ex.Message}");
        }
    }




















}
