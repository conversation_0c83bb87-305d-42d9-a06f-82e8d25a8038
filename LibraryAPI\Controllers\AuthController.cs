using Microsoft.AspNetCore.Mvc;
using LibraryAPI.Contracts.Request;
using LibraryAPI.Contracts.Response;
using LibraryAPI.Services;
using LibraryAPI.Models;
using Microsoft.EntityFrameworkCore;
using LibraryAPI.AppDataContext;
using System;
using System.Linq;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;

namespace LibraryAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly LibraryDbContext _dbContext;
        private readonly JwtTokenService _jwtTokenService;

        public AuthController(LibraryDbContext dbContext, JwtTokenService jwtTokenService)
        {
            _dbContext = dbContext;
            _jwtTokenService = jwtTokenService;
        }
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<IActionResult> Register(RegisterUserRequest request)
        {
            var userExists = await _dbContext.Users.AnyAsync(u => u.Email == request.Email);
            if (userExists)
                return BadRequest(new { message = "User already exists." });

            var user = new User
            {
                Name = request.Name,
                Email = request.Email,
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                Role = "Member" // Always set role to "Member" regardless of what was requested
            };

            _dbContext.Users.Add(user);
            await _dbContext.SaveChangesAsync();

            return Ok(new UserResponse
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email,
                Role = user.Role
            });
        }

        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login(LoginUserRequest request)
        {
            try
            {
                Console.WriteLine($"Login attempt for email: {request.Email}");

                // Find the user
                var user = await _dbContext.Users
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.Email == request.Email);

                if (user == null)
                {
                    Console.WriteLine("User not found");
                    return Unauthorized(new { message = "Invalid credentials." });
                }

                if (!BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
                {
                    Console.WriteLine("Password verification failed");
                    return Unauthorized(new { message = "Invalid credentials." });
                }

                Console.WriteLine($"User found: {user.Email}, Role: {user.Role}");

                var token = _jwtTokenService.GenerateToken(user);

                // Generate refresh token
                var refreshToken = _jwtTokenService.GenerateRefreshToken();

                // Create a new refresh token entity
                var refreshTokenEntity = new RefreshToken
                {
                    Token = refreshToken,
                    ExpiryDate = DateTime.UtcNow.AddDays(7),
                    UserId = user.Id,
                    IsRevoked = false
                };

                // Add the refresh token directly to the context
                _dbContext.RefreshTokens.Add(refreshTokenEntity);

                // Save changes
                await _dbContext.SaveChangesAsync();

                Console.WriteLine("Login successful, token generated");

                return Ok(new
                {
                    token,
                    refreshToken,
                    user = new UserResponse
                    {
                        Id = user.Id,
                        Name = user.Name,
                        Email = user.Email,
                        Role = user.Role
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Login error: {ex.Message}");
                return StatusCode(500, new { message = $"Error during login: {ex.Message}" });
            }
        }

        [HttpPost("refresh")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request)
        {
            if (request == null || string.IsNullOrEmpty(request.Token) || string.IsNullOrEmpty(request.RefreshToken))
                return BadRequest(new { message = "Invalid client request" });

            try
            {
                // Get principal from expired token
                var principal = _jwtTokenService.GetPrincipalFromExpiredToken(request.Token);
                var userId = principal.FindFirstValue(ClaimTypes.NameIdentifier);

                if (string.IsNullOrEmpty(userId))
                    return BadRequest(new { message = "Invalid token" });

                // Find user
                var user = await _dbContext.Users
                    .AsNoTracking() // Use AsNoTracking to avoid tracking issues
                    .FirstOrDefaultAsync(u => u.Id == Guid.Parse(userId));

                if (user == null)
                    return BadRequest(new { message = "User not found" });

                // Check if refresh token exists and is valid
                var storedRefreshToken = await _dbContext.RefreshTokens
                    .FirstOrDefaultAsync(rt => rt.Token == request.RefreshToken &&
                                              rt.UserId == Guid.Parse(userId) &&
                                              !rt.IsRevoked);

                if (storedRefreshToken == null)
                    return BadRequest(new { message = "Invalid refresh token" });

                if (storedRefreshToken.ExpiryDate < DateTime.UtcNow)
                    return BadRequest(new { message = "Refresh token expired" });

                // Generate new tokens
                var newToken = _jwtTokenService.GenerateToken(user);
                var newRefreshToken = _jwtTokenService.GenerateRefreshToken();

                // Mark old refresh token as revoked
                storedRefreshToken.IsRevoked = true;
                _dbContext.RefreshTokens.Update(storedRefreshToken);

                // Create new refresh token
                var refreshTokenEntity = new RefreshToken
                {
                    Token = newRefreshToken,
                    ExpiryDate = DateTime.UtcNow.AddDays(7),
                    UserId = user.Id,
                    IsRevoked = false
                };

                _dbContext.RefreshTokens.Add(refreshTokenEntity);
                await _dbContext.SaveChangesAsync();

                return Ok(new
                {
                    token = newToken,
                    refreshToken = newRefreshToken
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = $"Error refreshing token: {ex.Message}" });
            }
        }

        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                    return Unauthorized(new { message = "User ID not found in token." });

                // Get all active refresh tokens for the user
                var refreshTokens = await _dbContext.RefreshTokens
                    .Where(rt => rt.UserId == Guid.Parse(userId) && !rt.IsRevoked)
                    .ToListAsync();

                // Mark all tokens as revoked
                foreach (var token in refreshTokens)
                {
                    token.IsRevoked = true;
                    _dbContext.RefreshTokens.Update(token);
                }

                await _dbContext.SaveChangesAsync();

                return Ok(new { message = "Logged out successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = $"Error during logout: {ex.Message}" });
            }
        }

        [HttpGet("users")]
        [Authorize(Roles = "Admin")] // Only admins can access this endpoint
        public async Task<IActionResult> GetAllUsers()
        {
            try
            {
                var users = await _dbContext.Users.ToListAsync();
                var userResponses = users.Select(u => new UserResponse
                {
                    Id = u.Id,
                    Name = u.Name,
                    Email = u.Email,
                    Role = u.Role
                }).ToList();

                return Ok(userResponses);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = $"Error retrieving users: {ex.Message}" });
            }
        }
    }
}
