
using LibraryAPI.Models;
using Microsoft.EntityFrameworkCore;

namespace LibraryAPI.AppDataContext;

public class LibraryDbContext : DbContext
{
    
    public LibraryDbContext(DbContextOptions<LibraryDbContext> options)
        : base(options)
    {
    }

    public DbSet<Book> Books { get; set; }
    public DbSet<Author> Authors { get; set; }
    public DbSet<Member> Members {get;set;}
    public DbSet<Loan> Loans { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<RefreshToken> RefreshTokens { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Book to Author relationship (one-to-many)
        modelBuilder.Entity<Book>()
        .HasOne(b => b.Author)
        .WithMany(a => a.Books)
        .HasForeignKey(b => b.AuthorId);

        // Member to Loan relationship (one-to-one)
        modelBuilder.Entity<Member>()
          .HasOne(m => m.Loan)
          .WithOne(l => l.Member)
          .HasForeignKey<Loan>(l => l.MemberId);

        // Loan to Book relationship (one-to-one)
        modelBuilder.Entity<Loan>()
       .HasOne(l => l.Book)
       .WithOne(b => b.Loan)
       .HasForeignKey<Loan>(l => l.BookId);
        
        // User to RefreshToken relationship (one-to-many)
        modelBuilder.Entity<RefreshToken>()
            .HasOne(rt => rt.User)
            .WithMany(u => u.RefreshTokens)
            .HasForeignKey(rt => rt.UserId)
            .OnDelete(DeleteBehavior.Cascade);
        
        base.OnModelCreating(modelBuilder);
    }
}
