using LibraryAPI.Models;
using LibraryAPI.Contracts.Response;
using Mapster;

namespace LibraryAPI.Mappings
{
    public class MappingConfig : IRegister
    {
        public void Register(TypeAdapterConfig config)
        {
            // Configure Author mappings
            config.NewConfig<Author, AuthorDto>()
                .Map(dest => dest.Books, src => src.Books);
                
            // Configure Book mappings
            config.NewConfig<Book, BookDto>();
                
            // Configure Member mappings
            config.NewConfig<Member, MemberDto>()
                .Map(dest => dest.Loan, src => src.Loan);
                
            // Configure Loan mappings
            config.NewConfig<Loan, LoanDto>()
                .Map(dest => dest.Book, src => src.Book);
                
            // Add more mappings as needed
        }
    }
}

