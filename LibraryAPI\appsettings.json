{"DbSettings": {"ConnectionString": "Server=localhost;Database=LibraryDB;Integrated Security=true;TrustServerCertificate=true;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "Jwt": {"Key": "YourSuperSecretKeyHereMakeItLongAndComplex", "Issuer": "LibraryAPI", "Audience": "LibraryAPI"}, "Seeding": {"EnableSeeding": true, "SeedUsers": true, "SeedAuthors": false, "SeedBooks": false, "SeedMembers": false, "Users": {"AdminEmail": "<EMAIL>", "AdminPassword": "Admin123!", "AdminName": "System Administrator", "LibrarianEmail": "<EMAIL>", "LibrarianPassword": "Librarian123!", "LibrarianName": "Head Librarian", "MemberEmail": "<EMAIL>", "MemberPassword": "Member123!", "MemberName": "Demo Member"}}}