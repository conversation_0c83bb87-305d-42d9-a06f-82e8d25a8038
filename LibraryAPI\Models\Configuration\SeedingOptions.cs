namespace LibraryAPI.Models.Configuration
{
    public class SeedingOptions
    {
        public const string SectionName = "Seeding";
        
        public bool EnableSeeding { get; set; } = true;
        public bool SeedUsers { get; set; } = true;
        public bool SeedAuthors { get; set; } = false;
        public bool SeedBooks { get; set; } = false;
        public bool SeedMembers { get; set; } = false;
        
        public UserSeedingOptions Users { get; set; } = new UserSeedingOptions();
    }

    public class UserSeedingOptions
    {
        public string AdminEmail { get; set; } = "<EMAIL>";
        public string AdminPassword { get; set; } = "Admin123!";
        public string AdminName { get; set; } = "System Administrator";
        
        public string LibrarianEmail { get; set; } = "<EMAIL>";
        public string LibrarianPassword { get; set; } = "Librarian123!";
        public string LibrarianName { get; set; } = "Head Librarian";
        
        public string MemberEmail { get; set; } = "<EMAIL>";
        public string MemberPassword { get; set; } = "Member123!";
        public string MemberName { get; set; } = "Demo Member";
    }
}
