

using System.Runtime.CompilerServices;
using LibraryAPI.AppDataContext;
using LibraryAPI.Models;
using LibraryAPI.Services.Interface;
using Microsoft.EntityFrameworkCore;


namespace LibraryAPI.Services.Repository;

public class MemberRepository : IMemberRepository
{
    private readonly LibraryDbContext libraryDbContext;

    public MemberRepository(LibraryDbContext libraryDbContext)
    {
        this.libraryDbContext = libraryDbContext;
    }

    public async Task<Member> CreateMemberAsync(Member member)
    {
        var newMember = new Member
        {
            Name = member.Name,
            CreatedAt = DateTime.UtcNow.ToString()
        };
        
        libraryDbContext.Add(newMember);
        await libraryDbContext.SaveChangesAsync();
        return newMember;
       
    }

    public async Task<bool> DeleteMemberByIdAsync(Guid id)
    {
        var targetmember = await libraryDbContext.Members.FindAsync(id);
        if (targetmember == null)
        {
            return false;
        }
        targetmember.DeletedAt = DateTime.UtcNow.ToString();
        libraryDbContext.Members.Update(targetmember);
        await libraryDbContext.SaveChangesAsync();
        return true;

    }

    

    public async Task<IEnumerable<Member>> GetAllMembersAsync()
    {
        var allMembers = await libraryDbContext.Members
            .Where(m => m.DeletedAt == null)
            .Include(m => m.Loan)
                .ThenInclude(l => l != null ? l.Book : null)
            .ToListAsync();
        
        return allMembers;
    }

    public async Task<Member?> GetMemberByIdAsync(Guid id)
    {
        var targetmember = await libraryDbContext.Members
            .Where(m => m.DeletedAt == null)
            .Include(m => m.Loan)
                .ThenInclude(l => l != null ? l.Book : null)
            .FirstOrDefaultAsync(m => m.Id == id);
        
        if (targetmember == null)
        {
            return null;
        }
        return targetmember;
    }

    public async Task<Member?> UpdateMemberByIdAsync(Guid id, Member member)
    {
        var targetmember = await libraryDbContext.Members
        .Where(m => m.DeletedAt == null)
        .FirstOrDefaultAsync(m => m.Id == id);
        if (targetmember ==null){
            return null;
        }
        targetmember.Name = member.Name;
        targetmember.UpdatedAt = DateTime.UtcNow.ToString();
        await libraryDbContext.SaveChangesAsync();
        return targetmember;
    }
}
