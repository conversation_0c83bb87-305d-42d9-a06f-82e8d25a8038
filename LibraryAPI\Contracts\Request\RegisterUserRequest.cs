using System.ComponentModel.DataAnnotations;

namespace LibraryAPI.Contracts.Request;

public class RegisterUserRequest
{
    [Required]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [MinLength(6)]
    public string Password { get; set; } = string.Empty;
    // Role property removed as it's no longer used
}
