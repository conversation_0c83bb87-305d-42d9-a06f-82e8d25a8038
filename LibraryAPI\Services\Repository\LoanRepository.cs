using LibraryAPI.AppDataContext;
using LibraryAPI.Models;
using LibraryAPI.Services.Interface;
using Microsoft.EntityFrameworkCore;

namespace LibraryAPI.Services.Repository;

public class LoanRepository : ILoanRepository
{
    private readonly LibraryDbContext libraryDbContext;

    public LoanRepository(LibraryDbContext libraryDbContext)
    {
        this.libraryDbContext = libraryDbContext;
    }

    public async Task<Loan> CreateLoan(Loan loan)
    {
        var newLoan = await libraryDbContext.Loans.AddAsync(loan);
        await libraryDbContext.SaveChangesAsync();
        return newLoan.Entity;
        

    }




    public async Task<bool> DeleteLoanById(Guid id)
    {
        var targetloan = await libraryDbContext.Loans.FindAsync(id);
        if (targetloan == null)
        {
            return false;
        }
        targetloan.DeletedAt = DateTime.UtcNow.ToString();
        libraryDbContext.Loans.Update(targetloan);
        await libraryDbContext.SaveChangesAsync();
        return true;
    }


    

    public async Task<IEnumerable<Loan>> GetAllLoan()
    {
        var allLoans = await libraryDbContext.Loans
        .Where(l => l.DeletedAt == null)
        .Include(l => l.Book)
        .Include(l => l.Member)
        .ToListAsync();
        return allLoans;
    }

    public async Task<Loan?> GetLoanById(Guid id)
    {
        var targetloan = await libraryDbContext.Loans
        .Where(l => l.DeletedAt == null)  
        .Include(l => l.Book)  
        .Include(l => l.Member)  
        .FirstOrDefaultAsync(l => l.Id == id);  
        
        if(targetloan == null){
            return null;
        }
        return targetloan;
    }

    public async Task<Loan?> GetLoanByMemberId(Guid memberId)
    {
        var loan = await libraryDbContext.Loans
            .Where(l => l.DeletedAt == null && l.MemberId == memberId)
            .Include(l => l.Book)
            .Include(l => l.Member)
            .FirstOrDefaultAsync();
        
        return loan;
    }

    public async Task<Loan?> UpdateLoanById(Guid id, Loan loan)
    {
        var existingLoan = await libraryDbContext.Loans
            .Where(l => l.DeletedAt == null)
            .FirstOrDefaultAsync(l => l.Id == id);
        
        if (existingLoan == null)
        {
            return null;
        }
        
        // Update properties
        existingLoan.MemberId = loan.MemberId;
        existingLoan.BookId = loan.BookId;
        existingLoan.UpdatedAt = DateTime.UtcNow.ToString();
        
        libraryDbContext.Loans.Update(existingLoan);
        await libraryDbContext.SaveChangesAsync();
        
        // Reload the loan with related entities
        var updatedLoan = await libraryDbContext.Loans
            .Where(l => l.Id == id)
            .Include(l => l.Book)
            .Include(l => l.Member)
            .FirstOrDefaultAsync();
        
        return updatedLoan;
    }
}
